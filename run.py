#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
APS 车规芯片终测智能调度平台 - 应用启动脚本
版本: v2.0 (Flask + MySQL 架构)
"""

import sys
import os
import logging
import webbrowser
from threading import Timer
import traceback

# 在导入app之前设置基础日志级别
logging.getLogger().setLevel(logging.WARNING)

# 设置静默启动环境变量
os.environ['FLASK_QUIET_STARTUP'] = '1'

# 临时屏蔽第三方库和app模块的INFO级别日志
for logger_name in ['app', 'app.models', 'app.services']:
    logging.getLogger(logger_name).setLevel(logging.WARNING)

from app import create_app

# 导入统一配置管理器
from config.unified_config import get_unified_config

# 获取统一配置实例
config = get_unified_config()

# 确保必要目录存在 - 使用配置化路径
critical_dirs = [
    config.LOG_DIR,
    config.INSTANCE_DIR,
    config.STATIC_EXPORTS_DIR,
    config.DOWNLOAD_DIR,
    config.UPLOAD_DIR
]
for directory in critical_dirs:
    try:
        os.makedirs(directory, exist_ok=True)
        logging.debug(f"确保目录存在: {directory}")
    except Exception as e:
        logging.warning(f"创建目录失败 {directory}: {e}")

# 配置日志系统 - 使用配置化路径
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.WARNING if config.QUIET_STARTUP else getattr(logging, config.LOG_LEVEL))

file_handler = logging.FileHandler(config.get_log_file_path('app.log'), encoding='utf-8')
file_handler.setLevel(logging.DEBUG)  # 文件保留详细信息

logging.basicConfig(
    level=logging.DEBUG,  # 根日志器设为DEBUG
    format='%(message)s',   # 简化格式
    handlers=[console_handler, file_handler]
)

# 添加用户友好的日志过滤器
class UserFriendlyFilter(logging.Filter):
    def filter(self, record):
        # 过滤掉对用户无意义的技术信息
        ignore_patterns = [
            '✅.*蓝图注册', '✅.*API.*蓝图', '✅.*初始化成功', 
            'Server initialized', 'Python版本', '模块.*已安装',
            '上下文处理器', '数据库.*存在', '运行时环境检查完成',
            '无法导入CP订单模型', 'attempted relative import',
            '传统模型导入成功', '系统模型包已禁用', '已禁用',
            'INFO:app.models', 'INFO:app.', 'INFO:engineio',
            '✅.*模型.*验证', '✅.*开发环境', '已应用功能配置',
            '生产模型包已禁用', '失败跟踪系统已集成', '横向信息提取器',
            '解析器初始化', '通用Excel解析器', '手动排产API蓝图',
            '生产管理视图蓝图', '订单.*蓝图', '高并发.*蓝图',
            '已排产批次.*蓝图', '手动调整.*蓝图', '最终排产.*蓝图',
            '资源.*蓝图', '系统.*蓝图', '多级缓存.*蓝图',
            '并行计算.*蓝图', '认证.*蓝图', 'WIP批次.*蓝图',
            '所有API.*蓝图', 'API v3.*蓝图', 'API v3处于开发',
            '统一日志系统已配置', 'APS应用启动.*v2.1',
            '应用启动$'
        ]
        
        message = record.getMessage()
        for pattern in ignore_patterns:
            import re
            if re.search(pattern, message):
                return False
        return True

logger = logging.getLogger('APS-Platform')

# 为控制台处理器添加过滤器（过滤技术信息）
console_handler.addFilter(UserFriendlyFilter())

# 设置第三方库日志级别，减少噪音
logging.getLogger('werkzeug').setLevel(logging.WARNING)
logging.getLogger('engineio').setLevel(logging.ERROR)
logging.getLogger('socketio').setLevel(logging.ERROR)
logging.getLogger('apscheduler').setLevel(logging.WARNING)

def get_app_path():
    """获取应用程序路径，处理PyInstaller打包和开发环境"""
    if getattr(sys, 'frozen', False):
        # 运行在PyInstaller打包环境
        # sys._MEIPASS 是PyInstaller解压临时文件的路径
        return getattr(sys, '_MEIPASS', os.path.dirname(sys.executable))
    else:
        # 运行在常规Python环境
        return os.path.dirname(os.path.abspath(__file__))

def check_mysql_connection():
    """检查MySQL数据库连接和表结构"""
    try:
        # 导入必要模块
        import pymysql
        
        # 使用统一配置管理器获取数据库连接配置
        try:
            mysql_config = {
                'host': config.MYSQL_HOST,
                'port': config.MYSQL_PORT,
                'user': config.MYSQL_USER,
                'password': config.MYSQL_PASSWORD,
                'charset': config.MYSQL_CHARSET
            }
            logger.debug(f"使用统一配置管理器获取MySQL配置: {config.MYSQL_HOST}:{config.MYSQL_PORT}")
        except (AttributeError, Exception) as e:
            # 向后兼容：如果配置管理器不可用，使用默认配置
            logger.warning(f"配置管理器配置获取失败: {e}，使用默认MySQL配置")
            mysql_config = {
                'host': 'localhost',
                'port': 3306,
                'user': 'root',
                'password': 'WWWwww123!',
                'charset': 'utf8mb4'
            }
        
        # 需要检查的数据库和关键表 - 单数据库模式
        database_checks = {
            'aps': [
                # 业务表
                'et_wait_lot', 'wip_lot', 'eqp_status', 'et_ft_test_spec',
                'ET_UPH_EQP', 'ct', 'tcc_inv', 'lotprioritydone',
                # 系统表（已迁移到aps数据库）
                'users', 'user_permissions', 'menu_permissions',
                'devicepriorityconfig', 'lotpriorityconfig'
            ]
        }
        
        # 连接MySQL服务器
        conn = pymysql.connect(**mysql_config)
        cursor = conn.cursor()
        
        missing_items = []
        
        for db_name, required_tables in database_checks.items():
            # 检查数据库是否存在
            cursor.execute(f"SHOW DATABASES LIKE '{db_name}'")
            if not cursor.fetchone():
                logger.error(f"数据库 '{db_name}' 不存在")
                missing_items.append(f"数据库: {db_name}")
                continue
            
            logger.info(f"数据库 '{db_name}' 存在")
            
            # 检查关键表是否存在
            cursor.execute(f"USE {db_name}")
            for table_name in required_tables:
                cursor.execute(f"SHOW TABLES LIKE '{table_name}'")
                if cursor.fetchone():
                    logger.debug(f"表 '{db_name}.{table_name}' 存在")
                else:
                    logger.warning(f"表 '{db_name}.{table_name}' 不存在")
                    missing_items.append(f"表: {db_name}.{table_name}")
        
        conn.close()
        
        if missing_items:
            logger.error("❌ 数据库检查发现问题:")
            for item in missing_items:
                logger.error(f"   - {item}")
            logger.error("请运行: python run.py init-db")
            return False
        
        logger.info("MySQL 数据库和表结构检查完成")
        return True
        
    except ImportError as e:
        logger.error(f"❌ 缺少必要模块: {e}")
        logger.error("请运行: pip install -r requirements.txt")
        return False
    except Exception as e:
        logger.error(f"❌ MySQL 数据库检查失败: {e}")
        logger.error("请检查:")
        logger.error("  1. MySQL 服务器是否运行")
        logger.error("  2. 用户名密码是否正确")
        logger.error("  3. 网络连接是否正常")
        return False

def check_runtime_environment():
    """检查运行时环境和依赖"""
    try:
        # 检查Python版本
        python_version = sys.version_info
        if python_version < (3, 7):
            logger.error("❌ Python版本过低，需要Python 3.7+")
            return False
        
        logger.info(f"✅ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
        
        # 检查关键依赖
        required_modules = ['flask', 'pymysql', 'sqlalchemy', 'requests', 'pandas']
        missing_modules = []
        
        for module in required_modules:
            try:
                __import__(module)
                logger.debug(f"✅ 模块 {module} 已安装")
            except ImportError:
                missing_modules.append(module)
                logger.error(f"❌ 模块 {module} 未安装")
        
        if missing_modules:
            logger.error("请安装缺失的依赖:")
            logger.error("pip install -r requirements.txt")
            return False
        
        # 检查关键目录和文件（仅在开发环境中）
        if not getattr(sys, 'frozen', False):
            # 只在非打包环境中检查源码目录结构
            critical_paths = {
                'app/': '应用主目录',
                'app/services/data_source_manager.py': '数据源管理器',
                'app/api_v2/': 'API v2接口',
                'app/templates/': '模板目录',
                'app/static/': '静态资源',
                'config/__init__.py': '配置文件'
            }
            
            for path, description in critical_paths.items():
                if os.path.exists(path):
                    logger.debug(f"✅ {description}: {path}")
                else:
                    logger.error(f"❌ {description}不存在: {path}")
                    return False
        else:
            # 打包环境中，只检查关键模块是否可导入
            try:
                import app
                import app.services.data_source_manager
                import config
                logger.debug("✅ 打包环境：关键模块导入成功")
            except ImportError as e:
                logger.error(f"❌ 打包环境：模块导入失败: {e}")
                return False
        
        logger.info("✅ 运行时环境检查完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 环境检查失败: {e}")
        return False

def create_application():
    """创建APS应用实例"""
    try:
        # 设置工作目录
        app_path = get_app_path()
        logger.info(f"🏠 应用路径: {app_path}")
        os.chdir(app_path)
        
        # 检查运行时环境
        logger.info("🔍 检查运行时环境...")
        if not check_runtime_environment():
            logger.error("❌ 运行时环境检查失败")
            return None
        
        # 检查MySQL数据库
        logger.info("🔍 检查MySQL数据库...")
        if not check_mysql_connection():
            logger.error("❌ 数据库检查失败")
            logger.error("💡 请先运行: python run.py init-db")
            return None
        
        # 创建Flask应用实例
        logger.info("创建Flask应用...")
        app_result = create_app()
        
        if app_result is None:
            logger.error("❌ Flask应用创建失败")
            return None
        
        # 解包应用和SocketIO实例
        if isinstance(app_result, tuple):
            app, socketio = app_result
            # 检查SocketIO是否被禁用
            if not app.config.get('SOCKETIO_ENABLED', True):
                socketio = None
                logger.info("SocketIO已在PyInstaller环境中禁用")
        else:
            app = app_result
            socketio = None
        
        # 验证关键服务
        with app.app_context():
            try:
                # 检查数据源管理器
                from app.services.data_source_manager import DataSourceManager
                manager = DataSourceManager()
                status = manager.get_data_source_status()
                logger.info(f"📊 数据源状态: MySQL={'可用' if status.get('mysql_available') else '不可用'}")
                
                # 检查API路由
                from flask import url_for
                logger.debug("🔗 API路由注册正常")
                
            except Exception as e:
                logger.warning(f"⚠️ 服务验证部分失败: {e}")
        
        logger.info("应用创建成功")
        return app, socketio
        
    except Exception as e:
        logger.error(f"❌ 应用创建失败: {e}")
        traceback.print_exc()
        return None

def main():
    """主启动函数"""
    try:
        # 显示启动横幅
        print("\n🚀 APS 车规芯片终测智能调度平台 v2.0")
        print("=" * 50)
                
        # 检查命令行参数
        if len(sys.argv) > 1:
            if sys.argv[1] == 'init-db':
                logger.info("🔧 正在初始化MySQL数据库...")
                try:
                    from init_db import main as init_main
                    success = init_main()
                    if success:
                        logger.info("✅ 数据库初始化完成")
                        print("\n🎉 数据库初始化成功！现在可以启动应用:")
                        print("   python run.py")
                    else:
                        logger.error("❌ 数据库初始化失败")
                    sys.exit(0 if success else 1)
                except ImportError as e:
                    logger.error(f"❌ 无法导入初始化模块: {e}")
                    sys.exit(1)
                    
            elif sys.argv[1] in ['--help', '-h', 'help']:
                print("使用说明:")
                print("  python run.py              # 启动应用服务器")
                print("  python run.py init-db      # 初始化MySQL数据库")
                sys.exit(0)
                
            elif sys.argv[1] == 'migrate':
                logger.info("🔄 执行数据库迁移...")
                try:
                    from migrate_priority_tables import migrate_priority_tables
                    migrate_priority_tables()
                    logger.info("✅ 数据库迁移完成")
                    sys.exit(0)
                except Exception as e:
                    logger.error(f"❌ 数据库迁移失败: {e}")
                    sys.exit(1)
        
        # 创建应用实例
        print("🔍 正在初始化系统...")
        app_result = create_application()
        if app_result is None:
            print("❌ 系统启动失败")
            print("\n💡 解决建议:")
            print("1. 确保MySQL服务正在运行")
            print("2. 检查数据库连接配置")
            print("3. 运行数据库初始化: python run.py init-db")
            print("4. 查看详细日志: logs/app.log")
            sys.exit(1)
        
        # 解包应用和SocketIO实例
        if isinstance(app_result, tuple):
            app, socketio = app_result
        else:
            app = app_result
            socketio = None
        
        # 启动服务器 - 使用统一配置管理器
        host = config.FLASK_HOST  # 从.env文件加载，默认0.0.0.0
        port = config.FLASK_PORT  # 从.env文件加载，默认5000
        
        # 为浏览器访问准备正确的地址
        browser_host = 'localhost' if host == '0.0.0.0' else host
        
        print("✅ 系统启动成功！")
        print(f"🌐 本地访问地址: http://{browser_host}:{port}")
        if host == '0.0.0.0':
            # 获取本机局域网IP地址
            try:
                import socket
                local_ip = socket.gethostbyname(socket.gethostname())
                if local_ip.startswith('192.168.') or local_ip.startswith('10.') or local_ip.startswith('172.'):
                    print(f"🌐 局域网访问地址: http://{local_ip}:{port}")
            except:
                pass
        print("👤 默认账户: admin / admin")
        print("📱 支持浏览器: Chrome, Firefox, Edge")
        print("⏹️  按 Ctrl+C 停止服务")
        print("=" * 50)
        
        # 判断运行环境
        is_production = getattr(sys, 'frozen', False)
        if not is_production:
            # 开发环境：延迟打开浏览器
            logger.info("开发模式，将自动打开浏览器...")
            Timer(2.0, lambda: webbrowser.open(f'http://{browser_host}:{port}')).start()
        else:
            logger.info("生产模式")
        
        # 启动Flask应用
        if socketio:
            # 使用SocketIO运行，支持WebSocket
            socketio.run(
                app,
                host=host,
                port=port,
                debug=False,
                use_reloader=False,
                log_output=True,
                allow_unsafe_werkzeug=True  # 生产环境必需
            )
        else:
            # fallback到普通Flask
            app.run(
                host=host,
                port=port,
                debug=False,
                threaded=True,
                use_reloader=False  # 避免重载器问题
            )
        
    except KeyboardInterrupt:
        print("\n✅ APS平台已安全停止")
        sys.exit(0)
    except Exception as e:
        logger.error(f"❌ 启动失败: {e}")
        print(f"\n启动错误: {e}")
        traceback.print_exc()
        sys.exit(1)

if __name__ == '__main__':
    main() 