#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建缺失的ET_UPH_EQP表
"""

import pymysql
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_et_uph_eqp_table():
    """创建ET_UPH_EQP表"""
    
    # 数据库连接配置
    mysql_config = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': 'WWWwww123!',
        'database': 'aps',
        'charset': 'utf8mb4'
    }
    
    try:
        # 连接数据库
        conn = pymysql.connect(**mysql_config)
        cursor = conn.cursor()
        
        # 检查表是否已存在
        cursor.execute("SHOW TABLES LIKE 'ET_UPH_EQP'")
        if cursor.fetchone():
            logger.info("✅ ET_UPH_EQP表已存在")
            return True
        
        # 创建ET_UPH_EQP表
        create_table_sql = """
        CREATE TABLE ET_UPH_EQP (
            id INT AUTO_INCREMENT PRIMARY KEY,
            DEVICE VARCHAR(100) NOT NULL COMMENT '产品名称',
            PKG_PN VARCHAR(30) COMMENT '封装形式',
            STAGE VARCHAR(20) NOT NULL COMMENT '工序',
            UPH INT COMMENT '每小时产出量',
            HANDLER VARCHAR(50) NOT NULL COMMENT '分选机类型',
            FAC_ID CHAR(4) COMMENT '工厂编号',
            EDIT_STATE BOOLEAN DEFAULT TRUE COMMENT '可编辑状态',
            EDIT_TIME TIMESTAMP NULL COMMENT '编辑时间',
            EDIT_USER VARCHAR(50) COMMENT '编辑用户名称',
            EVENT VARCHAR(30) COMMENT '事件分类',
            EVENT_KEY VARCHAR(100) COMMENT '事件密钥',
            EVENT_TIME TIMESTAMP NULL COMMENT '事件时间',
            EVENT_USER VARCHAR(50) COMMENT '事件用户',
            EVENT_MSG TEXT COMMENT '事件信息',
            CREATE_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            CREATE_USER VARCHAR(50) COMMENT '创建的用户',
            INDEX idx_device (DEVICE),
            INDEX idx_stage (STAGE),
            INDEX idx_handler (HANDLER),
            INDEX idx_fac_id (FAC_ID)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='每小时产出表';
        """
        
        cursor.execute(create_table_sql)
        conn.commit()
        
        logger.info("✅ ET_UPH_EQP表创建成功")
        
        # 插入一些示例数据
        sample_data = [
            ('MCU001', 'QFP48', 'FT', 1200, 'Handler_A', 'F001', True, None, None, None, None, None, None, None, '2025-01-01 00:00:00', 'system'),
            ('MCU002', 'QFN32', 'FT', 1000, 'Handler_B', 'F001', True, None, None, None, None, None, None, None, '2025-01-01 00:00:00', 'system'),
            ('MCU003', 'BGA64', 'FT', 800, 'Handler_C', 'F001', True, None, None, None, None, None, None, None, '2025-01-01 00:00:00', 'system'),
        ]
        
        insert_sql = """
        INSERT INTO ET_UPH_EQP 
        (DEVICE, PKG_PN, STAGE, UPH, HANDLER, FAC_ID, EDIT_STATE, CREATE_TIME, CREATE_USER)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        
        cursor.executemany(insert_sql, sample_data)
        conn.commit()
        
        logger.info(f"✅ 插入了 {len(sample_data)} 条示例数据")
        
        # 验证表创建
        cursor.execute("SELECT COUNT(*) FROM ET_UPH_EQP")
        count = cursor.fetchone()[0]
        logger.info(f"✅ ET_UPH_EQP表验证成功，共有 {count} 条记录")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 创建ET_UPH_EQP表失败: {e}")
        return False
        
    finally:
        if 'conn' in locals():
            conn.close()

def main():
    """主函数"""
    print("🔧 创建缺失的ET_UPH_EQP表")
    print("=" * 40)
    
    success = create_et_uph_eqp_table()
    
    if success:
        print("✅ ET_UPH_EQP表创建完成")
        print("💡 现在可以重新启动应用: python run.py")
    else:
        print("❌ ET_UPH_EQP表创建失败")
        print("💡 请检查MySQL连接和权限")

if __name__ == '__main__':
    main() 