#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查stage_mapping_config表的实际状态
"""

import pymysql

def check_stage_mapping_config():
    """检查stage_mapping_config表的实际状态"""
    try:
        connection = pymysql.connect(
            host='localhost',
            user='root', 
            password='WWWwww123!',
            database='aps',
            charset='utf8mb4'
        )
        
        with connection.cursor() as cursor:
            # 检查stage_mapping_config表是否存在
            cursor.execute("SHOW TABLES LIKE 'stage_mapping_config'")
            table_exists = cursor.fetchone()
            print(f'表是否存在: {bool(table_exists)}')
            
            if table_exists:
                # 检查表中的记录数量
                cursor.execute('SELECT COUNT(*) FROM stage_mapping_config')
                count = cursor.fetchone()[0]
                print(f'总记录数: {count}')
                
                if count > 0:
                    # 检查是否有ROOM-TTR-FT相关的映射
                    cursor.execute("SELECT * FROM stage_mapping_config WHERE source_stage LIKE '%ROOM%TTR%' OR target_stage LIKE '%ROOM%TTR%'")
                    room_ttr_mappings = cursor.fetchall()
                    print(f'ROOM-TTR相关映射数量: {len(room_ttr_mappings)}')
                    
                    if room_ttr_mappings:
                        print('ROOM-TTR相关映射详情:')
                        for mapping in room_ttr_mappings:
                            print(f'  ID: {mapping[0]}, {mapping[1]} -> {mapping[2]} ({mapping[3]}, active: {mapping[5]})')
                    
                    # 显示所有映射规则
                    cursor.execute('SELECT source_stage, target_stage, mapping_type, is_active FROM stage_mapping_config ORDER BY priority DESC, id')
                    all_mappings = cursor.fetchall()
                    print(f'\n所有映射规则 ({len(all_mappings)}条):')
                    for mapping in all_mappings:
                        print(f'  {mapping[0]} -> {mapping[1]} ({mapping[2]}, active: {mapping[3]})')
                else:
                    print('表存在但为空')
            else:
                print('表不存在')
        
        connection.close()
        print('\n数据库连接成功')
        
    except Exception as e:
        print(f'数据库连接失败: {e}')

if __name__ == '__main__':
    check_stage_mapping_config()
