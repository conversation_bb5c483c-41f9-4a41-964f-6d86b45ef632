#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查当前数据库状态，确认清空后的数据情况
"""

import pymysql

def check_current_data():
    """检查当前数据库状态"""
    try:
        connection = pymysql.connect(
            host='localhost',
            user='root', 
            password='WWWwww123!',
            database='aps',
            charset='utf8mb4'
        )
        
        with connection.cursor() as cursor:
            print("=== 检查当前数据库状态 ===\n")
            
            # 检查7个被清空的表
            tables_to_check = [
                'et_wait_lot',      # 等待批次表
                'et_uph_eqp',       # UPH设备表
                'et_recipe_file',   # 配方文件表
                'et_ft_test_spec',  # 测试规范表
                'ct',               # CT数据表
                'wip_lot',          # WIP批次表
                'eqp_status'        # 设备状态表
            ]
            
            print("📊 被清空的表状态:")
            for table in tables_to_check:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"   {table}: {count:,} 条记录")
            
            # 检查关键的未被清空的表
            other_tables = [
                'et_handler_config',    # 设备配置表
                'stage_mapping_config', # STAGE映射配置表
                'et_device_priority',   # 设备优先级表
                'et_lot_priority'       # 批次优先级表
            ]
            
            print(f"\n📋 其他关键表状态:")
            for table in other_tables:
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    count = cursor.fetchone()[0]
                    print(f"   {table}: {count:,} 条记录")
                except Exception as e:
                    print(f"   {table}: 查询失败 - {e}")
            
            # 检查是否有等待批次数据
            print(f"\n🔍 详细检查等待批次数据:")
            cursor.execute("SELECT COUNT(*) FROM et_wait_lot")
            wait_lot_count = cursor.fetchone()[0]
            print(f"   等待批次总数: {wait_lot_count}")
            
            if wait_lot_count > 0:
                cursor.execute("""
                    SELECT DEVICE, STAGE, COUNT(*) as count 
                    FROM et_wait_lot 
                    GROUP BY DEVICE, STAGE 
                    ORDER BY count DESC 
                    LIMIT 10
                """)
                wait_lots = cursor.fetchall()
                print(f"   前10个产品工序组合:")
                for lot in wait_lots:
                    print(f"     {lot[0]} - {lot[1]}: {lot[2]}个批次")
            
            # 检查测试规范数据
            print(f"\n🧪 详细检查测试规范数据:")
            cursor.execute("SELECT COUNT(*) FROM et_ft_test_spec")
            test_spec_count = cursor.fetchone()[0]
            print(f"   测试规范总数: {test_spec_count}")
            
            if test_spec_count > 0:
                cursor.execute("""
                    SELECT DEVICE, STAGE, COUNT(*) as count 
                    FROM et_ft_test_spec 
                    GROUP BY DEVICE, STAGE 
                    ORDER BY count DESC 
                    LIMIT 10
                """)
                test_specs = cursor.fetchall()
                print(f"   前10个产品工序组合:")
                for spec in test_specs:
                    print(f"     {spec[0]} - {spec[1]}: {spec[2]}条规范")
            
            # 检查设备配置数据
            print(f"\n⚙️ 详细检查设备配置数据:")
            cursor.execute("SELECT COUNT(*) FROM et_handler_config")
            handler_count = cursor.fetchone()[0]
            print(f"   设备配置总数: {handler_count}")
            
            if handler_count > 0:
                cursor.execute("""
                    SELECT HANDLER_ID, DEVICE, COUNT(*) as count 
                    FROM et_handler_config 
                    GROUP BY HANDLER_ID, DEVICE 
                    ORDER BY count DESC 
                    LIMIT 10
                """)
                handlers = cursor.fetchall()
                print(f"   前10个设备配置:")
                for handler in handlers:
                    print(f"     {handler[0]} - {handler[1]}: {handler[2]}条配置")
        
        connection.close()
        print('\n✅ 数据库检查完成')
        
    except Exception as e:
        print(f'❌ 数据库连接失败: {e}')

if __name__ == '__main__':
    check_current_data()
