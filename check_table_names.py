#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库中的实际表名
"""

import pymysql

def check_table_names():
    """检查数据库中的表名"""
    
    mysql_config = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': 'WWWwww123!',
        'database': 'aps',
        'charset': 'utf8mb4'
    }
    
    try:
        conn = pymysql.connect(**mysql_config)
        cursor = conn.cursor()
        
        # 获取所有表名
        cursor.execute("SHOW TABLES")
        tables = [row[0] for row in cursor.fetchall()]
        
        print("📋 数据库中的所有表:")
        print("=" * 50)
        
        for i, table in enumerate(sorted(tables), 1):
            print(f"{i:2d}. {table}")
        
        print("=" * 50)
        print(f"总计: {len(tables)} 个表")
        
        # 检查UPH相关表
        uph_tables = [t for t in tables if 'uph' in t.lower()]
        if uph_tables:
            print(f"\n🔍 UPH相关表: {uph_tables}")
        else:
            print("\n❌ 没有找到UPH相关表")
        
        # 检查大小写敏感的表名
        print(f"\n🔍 检查 'ET_UPH_EQP' 和 'et_uph_eqp':")
        if 'ET_UPH_EQP' in tables:
            print("✅ 'ET_UPH_EQP' 存在")
        elif 'et_uph_eqp' in tables:
            print("✅ 'et_uph_eqp' 存在")
        else:
            print("❌ 两个都不存在")
        
        return tables
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return []
        
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == '__main__':
    check_table_names() 