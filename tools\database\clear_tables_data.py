#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
APS 数据库表数据清空工具
清空指定的7个表中的所有数据，保留表结构
"""

import os
import sys
import logging
import pymysql
from datetime import datetime

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/clear_tables.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('APS-Clear')

def get_mysql_connection(database='aps'):
    """获取MySQL数据库连接"""
    try:
        config = {
            'host': 'localhost',
            'port': 3306,
            'user': 'root',
            'password': 'WWWwww123!',
            'database': database,
            'charset': 'utf8mb4',
            'autocommit': True
        }
        
        connection = pymysql.connect(**config)
        logger.info(f"✅ MySQL连接成功: {config['host']}:{config['port']}/{database}")
        return connection
        
    except Exception as e:
        logger.error(f"❌ MySQL连接失败: {e}")
        raise

def check_table_exists(cursor, table_name):
    """检查表是否存在"""
    cursor.execute(f"SHOW TABLES LIKE '{table_name}'")
    return cursor.fetchone() is not None

def get_table_row_count(cursor, table_name):
    """获取表的记录数"""
    try:
        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        return cursor.fetchone()[0]
    except Exception:
        return 0

def backup_table_data(cursor, table_name):
    """备份表数据（可选）"""
    try:
        backup_table_name = f"{table_name}_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        cursor.execute(f"CREATE TABLE {backup_table_name} AS SELECT * FROM {table_name}")
        logger.info(f"✅ 表 {table_name} 数据已备份到 {backup_table_name}")
        return backup_table_name
    except Exception as e:
        logger.warning(f"⚠️ 备份表 {table_name} 失败: {e}")
        return None

def clear_table_data(cursor, table_name):
    """清空表数据"""
    try:
        # 使用TRUNCATE TABLE清空数据（比DELETE FROM更快）
        cursor.execute(f"TRUNCATE TABLE {table_name}")
        logger.info(f"✅ 表 {table_name} 数据已清空")
        return True
    except Exception as e:
        # 如果TRUNCATE失败，尝试使用DELETE
        try:
            cursor.execute(f"DELETE FROM {table_name}")
            logger.info(f"✅ 表 {table_name} 数据已清空（使用DELETE）")
            return True
        except Exception as e2:
            logger.error(f"❌ 清空表 {table_name} 失败: {e2}")
            return False

def main():
    """主函数"""
    print("🧹 APS 数据库表数据清空工具")
    print("=" * 60)
    
    # 需要清空的7个表
    tables_to_clear = [
        'et_wait_lot',      # 等待批次表
        'et_uph_eqp',       # UPH设备表
        'et_recipe_file',   # 配方文件表
        'et_ft_test_spec',  # 测试规范表
        'ct',               # CT数据表
        'wip_lot',          # WIP批次表
        'eqp_status'        # 设备状态表
    ]
    
    print(f"📋 计划清空以下 {len(tables_to_clear)} 个表的数据:")
    for i, table in enumerate(tables_to_clear, 1):
        print(f"   {i}. {table}")
    
    print("\n⚠️ 警告: 此操作将永久删除表中的所有数据！")
    print("💡 建议: 操作前请确保已有数据备份")
    
    # 用户确认
    print("\n选择操作模式:")
    print("1. 清空数据（不备份）")
    print("2. 备份后清空数据（推荐）")
    print("3. 仅查看表状态")
    print("4. 取消操作")
    
    choice = input("\n请选择 (1/2/3/4): ").strip()
    
    if choice == '4':
        print("❌ 操作已取消")
        return
    
    try:
        # 连接数据库
        connection = get_mysql_connection()
        cursor = connection.cursor()
        
        # 检查表状态
        print(f"\n📊 检查表状态:")
        table_status = {}
        for table_name in tables_to_clear:
            if check_table_exists(cursor, table_name):
                row_count = get_table_row_count(cursor, table_name)
                table_status[table_name] = row_count
                print(f"   ✅ {table_name}: {row_count:,} 条记录")
            else:
                table_status[table_name] = -1
                print(f"   ❌ {table_name}: 表不存在")
        
        if choice == '3':
            print("\n📋 表状态检查完成")
            return
        
        # 执行清空操作
        if choice in ['1', '2']:
            print(f"\n🧹 开始清空表数据...")
            
            cleared_tables = []
            failed_tables = []
            backup_info = []
            
            for table_name in tables_to_clear:
                if table_status[table_name] == -1:
                    print(f"⏭️ 跳过不存在的表: {table_name}")
                    continue
                
                if table_status[table_name] == 0:
                    print(f"⏭️ 跳过空表: {table_name}")
                    continue
                
                print(f"\n🔄 处理表: {table_name} ({table_status[table_name]:,} 条记录)")
                
                # 备份数据（如果选择了备份模式）
                backup_table = None
                if choice == '2':
                    backup_table = backup_table_data(cursor, table_name)
                    if backup_table:
                        backup_info.append(f"{table_name} -> {backup_table}")
                
                # 清空数据
                if clear_table_data(cursor, table_name):
                    cleared_tables.append(table_name)
                else:
                    failed_tables.append(table_name)
            
            # 输出结果
            print(f"\n" + "="*60)
            print(f"🎉 数据清空操作完成！")
            print(f"="*60)
            print(f"📊 操作统计:")
            print(f"   ✅ 成功清空: {len(cleared_tables)} 个表")
            print(f"   ❌ 清空失败: {len(failed_tables)} 个表")
            
            if cleared_tables:
                print(f"\n✅ 成功清空的表:")
                for table in cleared_tables:
                    print(f"   • {table}")
            
            if failed_tables:
                print(f"\n❌ 清空失败的表:")
                for table in failed_tables:
                    print(f"   • {table}")
            
            if backup_info:
                print(f"\n💾 备份信息:")
                for info in backup_info:
                    print(f"   • {info}")
                print(f"\n💡 如需恢复数据，可使用备份表")
        
        cursor.close()
        connection.close()
        
    except Exception as e:
        logger.error(f"❌ 操作失败: {e}")
        print(f"\n💥 操作失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
