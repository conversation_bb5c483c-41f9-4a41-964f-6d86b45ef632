#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查ET_FT_TEST_SPEC表中JWQ7101SOTB-J115_TR1产品的测试规范
"""

import pymysql

def check_test_spec():
    """检查测试规范数据"""
    try:
        connection = pymysql.connect(
            host='localhost',
            user='root', 
            password='WWWwww123!',
            database='aps',
            charset='utf8mb4'
        )
        
        with connection.cursor() as cursor:
            # 1. 检查JWQ7101SOTB-J115_TR1产品的测试规范
            print("=== 检查JWQ7101SOTB-J115_TR1产品的测试规范 ===")
            cursor.execute("""
                SELECT DEVICE, STAGE, PKG_PN, APPROVAL_STATE, TEST_SPEC_ID 
                FROM et_ft_test_spec 
                WHERE DEVICE LIKE '%JWQ7101SOTB%' OR DEVICE LIKE '%J115%'
                ORDER BY DEVICE, STAGE
            """)
            jwq_specs = cursor.fetchall()
            print(f'JWQ7101SOTB相关规范数量: {len(jwq_specs)}')
            for spec in jwq_specs:
                print(f'  DEVICE: {spec[0]}, STAGE: {spec[1]}, PKG_PN: {spec[2]}, STATE: {spec[3]}')
            
            # 2. 检查所有包含ROOM-TTR的测试规范
            print("\n=== 检查所有ROOM-TTR相关的测试规范 ===")
            cursor.execute("""
                SELECT DEVICE, STAGE, PKG_PN, APPROVAL_STATE 
                FROM et_ft_test_spec 
                WHERE STAGE LIKE '%ROOM%TTR%'
                ORDER BY DEVICE, STAGE
            """)
            room_ttr_specs = cursor.fetchall()
            print(f'ROOM-TTR相关规范数量: {len(room_ttr_specs)}')
            for spec in room_ttr_specs:
                print(f'  DEVICE: {spec[0]}, STAGE: {spec[1]}, PKG_PN: {spec[2]}, STATE: {spec[3]}')
            
            # 3. 检查ET_FT_TEST_SPEC表中所有不同的STAGE值
            print("\n=== ET_FT_TEST_SPEC表中所有STAGE值 ===")
            cursor.execute("SELECT DISTINCT STAGE FROM et_ft_test_spec ORDER BY STAGE")
            all_stages = cursor.fetchall()
            print(f'总共有 {len(all_stages)} 种不同的STAGE值:')
            for stage in all_stages:
                print(f'  {stage[0]}')
            
            # 4. 检查是否有精确匹配ROOM-TTR的规范
            print("\n=== 检查ROOM-TTR精确匹配 ===")
            cursor.execute("""
                SELECT DEVICE, STAGE, PKG_PN, APPROVAL_STATE 
                FROM et_ft_test_spec 
                WHERE STAGE = 'ROOM-TTR'
                ORDER BY DEVICE
            """)
            exact_room_ttr = cursor.fetchall()
            print(f'STAGE=ROOM-TTR的规范数量: {len(exact_room_ttr)}')
            for spec in exact_room_ttr:
                print(f'  DEVICE: {spec[0]}, STAGE: {spec[1]}, PKG_PN: {spec[2]}, STATE: {spec[3]}')
        
        connection.close()
        print('\n数据库连接成功')
        
    except Exception as e:
        print(f'数据库连接失败: {e}')

if __name__ == '__main__':
    check_test_spec()
