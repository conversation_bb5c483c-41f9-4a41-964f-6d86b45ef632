#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
APS 网络访问修复脚本
解决其他电脑无法访问*************:5000的问题
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def create_env_file():
    """创建.env配置文件"""
    env_content = """# APS 车规芯片终测智能调度平台 - 环境变量配置
# 允许外部网络访问配置

# ==============================================
# 数据库配置 (MySQL)
# ==============================================
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=WWWwww123!
MYSQL_CHARSET=utf8mb4

# ==============================================
# Flask应用配置 - 允许外部访问
# ==============================================
FLASK_HOST=0.0.0.0
FLASK_PORT=5000
SECRET_KEY=dev-secret-key-change-in-production
FLASK_ENV=development

# ==============================================
# 系统配置
# ==============================================
TIMEZONE=Asia/Shanghai
DEFAULT_PAGE_SIZE=1000
DEFAULT_UPH=1000
MAX_WORKERS=10

# ==============================================
# 文件路径配置
# ==============================================
LOG_DIR=logs
UPLOAD_DIR=uploads
DOWNLOAD_DIR=downloads
INSTANCE_DIR=instance
STATIC_EXPORTS_DIR=static/exports

# ==============================================
# 管理员配置
# ==============================================
ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin

# ==============================================
# 调试配置
# ==============================================
LOG_LEVEL=INFO
QUIET_STARTUP=0
DEBUG=True
"""
    
    env_file = Path('.env')
    if env_file.exists():
        print(f"⚠️  .env文件已存在，备份为.env.backup")
        shutil.copy2(env_file, '.env.backup')
    
    with open(env_file, 'w', encoding='utf-8') as f:
        f.write(env_content)
    
    print(f"✅ 已创建.env文件，配置Flask监听地址为0.0.0.0")

def add_firewall_rule():
    """添加Windows防火墙规则"""
    try:
        # 检查是否已存在规则
        result = subprocess.run(
            ['netsh', 'advfirewall', 'firewall', 'show', 'rule', 'name="APS Flask App"'],
            capture_output=True, text=True, shell=True
        )
        
        # 处理可能的None返回值
        stdout = result.stdout or ""
        
        if "没有找到规则" in stdout or "No rules match" in stdout:
            # 添加入站规则
            subprocess.run([
                'netsh', 'advfirewall', 'firewall', 'add', 'rule',
                'name="APS Flask App"',
                'dir=in',
                'action=allow',
                'protocol=TCP',
                'localport=5000',
                'description="APS Flask应用端口5000入站规则"'
            ], shell=True, check=True)
            
            # 添加出站规则
            subprocess.run([
                'netsh', 'advfirewall', 'firewall', 'add', 'rule',
                'name="APS Flask App Outbound"',
                'dir=out',
                'action=allow',
                'protocol=TCP',
                'localport=5000',
                'description="APS Flask应用端口5000出站规则"'
            ], shell=True, check=True)
            
            print("✅ 已添加Windows防火墙规则")
        else:
            print("ℹ️  Windows防火墙规则已存在")
            
    except subprocess.CalledProcessError as e:
        print(f"⚠️ 防火墙规则添加失败: {e}")
        print("请以管理员身份运行此脚本")
    except Exception as e:
        print(f"⚠️ 防火墙配置出错: {e}")
        print("请手动检查防火墙设置")

def check_current_config():
    """检查当前配置"""
    print("🔍 检查当前网络配置...")
    
    # 检查端口监听状态
    result = subprocess.run(
        ['netstat', '-an'], 
        capture_output=True, text=True, shell=True
    )
    
    if "127.0.0.1:5000" in result.stdout:
        print("❌ 当前Flask应用只监听在127.0.0.1:5000（仅本地访问）")
        return False
    elif "0.0.0.0:5000" in result.stdout:
        print("✅ Flask应用已监听在0.0.0.0:5000（允许外部访问）")
        return True
    else:
        print("ℹ️ 端口5000未在监听")
        return False

def get_local_ip():
    """获取本机局域网IP地址"""
    try:
        result = subprocess.run(
            ['ipconfig'], 
            capture_output=True, text=True, shell=True
        )
        
        # 查找IPv4地址
        lines = result.stdout.split('\n')
        for line in lines:
            if 'IPv4' in line and '192.168.' in line:
                ip = line.split(':')[-1].strip()
                return ip
        
        return "*************"  # 默认值
    except:
        return "*************"

def main():
    """主函数"""
    print("🚀 APS 网络访问修复脚本")
    print("=" * 50)
    
    # 检查当前配置
    current_status = check_current_config()
    
    # 创建.env文件
    print("\n📝 创建配置文件...")
    create_env_file()
    
    # 添加防火墙规则
    print("\n🛡️ 配置防火墙规则...")
    add_firewall_rule()
    
    # 获取本机IP
    local_ip = get_local_ip()
    
    print("\n" + "=" * 50)
    print("✅ 网络访问配置完成！")
    print(f"🌐 其他电脑可以通过以下地址访问:")
    print(f"   http://{local_ip}:5000")
    print(f"👤 默认登录账户: admin / admin")
    print("\n📋 下一步操作:")
    print("1. 重启Flask应用: python run.py")
    print("2. 在其他电脑浏览器中访问上述地址")
    print("3. 如果仍有问题，请检查:")
    print("   - 两台电脑是否在同一WiFi网络")
    print("   - 防火墙是否允许端口5000")
    print("   - 路由器是否阻止了内网通信")
    
    if not current_status:
        print("\n⚠️ 注意：需要重启Flask应用才能生效！")

if __name__ == '__main__':
    main() 